<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class ContactController extends Controller
{
    public function index()
    {
        return view('contact');
    }

    public function submit(Request $request)
    {
        // Form data validation
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'address' => 'nullable|string|max:255',
            'service_type' => 'required|string',
            'urgency' => 'required|string',
            'message' => 'required|string|max:2000',
            'consent' => 'required|accepted',
            'attachment' => 'nullable|file|mimes:jpg,jpeg,png,pdf,doc,docx|max:5120', // Max 5MB (5120 KB)

        ], [
            'first_name.required' => 'First name is required.',
            'last_name.required' => 'Last name is required.',
            'email.required' => 'Email is required.',
            'email.email' => 'Email must be valid.',
            'phone.required' => 'Phone number is required.',
            'service_type.required' => 'Service type is required.',
            'urgency.required' => 'Urgency level is required.',
            'message.required' => 'Project description is required.',
            'consent.required' => 'You must agree to be contacted.',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Data processing (here we simulate email sending)
        $contactData = $request->all();

        // In a real project, we would send an email here
        // Mail::to('<EMAIL>')->send(new ContactFormMail($contactData));

        // Log the request for simulation
        \Log::info('New contact request', $contactData);

        return redirect()->route('contact')->with('success',
            'Your request has been sent successfully! We will contact you within 24 hours.');
    }
}

