<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class AboutController extends Controller
{
    public function index()
    {
        $values = [
            [
                'icon' => 'fas fa-star',
                'title' => 'Excellence',
                'description' => 'We aim for excellence in every project, using best practices and the most advanced technologies.'
            ],
            [
                'icon' => 'fas fa-handshake',
                'title' => 'Trust',
                'description' => 'Our clients\' trust is our most precious asset. We cultivate it through our transparency and reliability.'
            ],
            [
                'icon' => 'fas fa-leaf',
                'title' => 'Sustainability',
                'description' => 'We prioritize sustainable and ecological solutions for a responsible energy future.'
            ]
        ];

        $team = [
            [
                'name' => '<PERSON>',
                'role' => 'Founder & Director',
                'description' => 'Passionate electrician with over 20 years of experience. Expert in industrial and residential installations.',
                'avatar' => 'fas fa-user-circle'
            ],
            [
                'name' => '<PERSON>',
                'role' => 'Technical Manager',
                'description' => 'Specialized in security systems and home automation. Certified in renewable energy.',
                'avatar' => 'fas fa-user-circle'
            ],
            [
                'name' => '<PERSON>',
                'role' => 'Team Leader',
                'description' => 'Expert in emergency repair and code compliance. Continuous training in new technologies.',
                'avatar' => 'fas fa-user-circle'
            ]
        ];

        $certifications = [
            [
                'icon' => 'fas fa-certificate',
                'title' => 'NECA Certified',
                'description' => 'National Electrical Contractors Association certification for energy efficiency work.'
            ],
            [
                'icon' => 'fas fa-shield-alt',
                'title' => 'Liability Insurance',
                'description' => 'Comprehensive liability insurance for all our electrical installation work.'
            ],
            [
                'icon' => 'fas fa-tools',
                'title' => 'Licensed Electrician',
                'description' => 'Professional qualification for electrical and energy companies.'
            ],
            [
                'icon' => 'fas fa-leaf',
                'title' => 'EV Charging Certified',
                'description' => 'Electric Vehicle Charging Infrastructure - Level 2 Certified.'
            ]
        ];

        return view('about', compact('values', 'team', 'certifications'));
    }
}

