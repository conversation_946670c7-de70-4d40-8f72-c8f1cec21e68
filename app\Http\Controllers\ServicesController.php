<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class ServicesController extends Controller
{
    public function index()
    {
        $services = [
            [
                'id' => 'installation',
                'icon' => 'fas fa-home',
                'title' => 'Electrical Installation',
                'subtitle' => 'Complete and compliant installations',
                'description' => 'We perform complete electrical installation for your home or professional premises, strictly respecting NEC standards.',
                'features' => [
                    'Electrical panels',
                    'Outlets and switches',
                    'Interior and exterior lighting',
                    'Electric heating',
                    'Communication networks'
                ],
                'price' => 'Starting from $150'
            ],
            [
                'id' => 'depannage',
                'icon' => 'fas fa-tools',
                'title' => 'Emergency Repair',
                'subtitle' => '24/7 intervention',
                'description' => 'Electrical failure, short circuit, tripping breaker? Our team intervenes quickly to diagnose and repair your electrical problems.',
                'features' => [
                    'Precise diagnosis',
                    'Immediate repair',
                    'Component replacement',
                    'Service restoration',
                    'Preventive advice'
                ],
                'price' => 'Service call $80'
            ],
            [
                'id' => 'mise-aux-normes',
                'icon' => 'fas fa-shield-alt',
                'title' => 'Code Compliance',
                'subtitle' => 'Compliance and safety',
                'description' => 'Bringing your electrical installations into compliance with the latest regulations. Essential for selling or renting your property.',
                'features' => [
                    'Complete diagnosis',
                    'Panel upgrade',
                    'GFCI installation',
                    'Grounding system',
                    'Compliance certificate'
                ],
                'price' => 'Starting from $800'
            ],
            [
                'id' => 'eclairage-led',
                'icon' => 'fas fa-lightbulb',
                'title' => 'LED Lighting',
                'subtitle' => 'Economy and performance',
                'description' => 'Modernize your lighting with LED technology. Reduce your energy consumption by up to 80% while improving the quality of your lighting.',
                'features' => [
                    'Recessed spotlights',
                    'LED strips',
                    'Outdoor lighting',
                    'Dimmers',
                    'Smart lighting'
                ],
                'price' => 'Starting from $45'
            ],
            [
                'id' => 'securite',
                'icon' => 'fas fa-video',
                'title' => 'Security Systems',
                'subtitle' => 'Protection and surveillance',
                'description' => 'Protect your home or business with our electronic security solutions. Installation and maintenance of complete systems.',
                'features' => [
                    'Surveillance cameras',
                    'Intrusion alarms',
                    'Access control',
                    'Intercom/Video phone',
                    'Remote monitoring'
                ],
                'price' => 'Starting from $350'
            ],
            [
                'id' => 'borne-recharge',
                'icon' => 'fas fa-charging-station',
                'title' => 'Charging Stations',
                'subtitle' => 'Electric mobility',
                'description' => 'Installation of charging stations for electric vehicles. Solutions adapted to your needs, from reinforced outlet to smart charging station.',
                'features' => [
                    'Reinforced outlet',
                    'Wallbox 7kW/22kW',
                    'Smart charging stations',
                    'Load management',
                    '30% tax credit'
                ],
                'price' => 'Starting from $600'
            ]
        ];

        $process = [
            [
                'step' => 1,
                'title' => 'Contact',
                'description' => 'You contact us by phone or via our form. We analyze your request.'
            ],
            [
                'step' => 2,
                'title' => 'Diagnosis',
                'description' => 'We visit to assess your needs and establish a precise diagnosis.'
            ],
            [
                'step' => 3,
                'title' => 'Quote',
                'description' => 'We offer you a detailed and transparent quote, with no obligation on your part.'
            ],
            [
                'step' => 4,
                'title' => 'Implementation',
                'description' => 'We carry out the work within the agreed timeframe with constant quality monitoring.'
            ]
        ];

        return view('services', compact('services', 'process'));
    }
}

