@extends('layouts.app')

@section('title', 'Contact - Velect | Request a Quote Request')
@section('meta_description', 'Contact Velect for your electrical projects. Free quote within 24h. Service across Sydney. 24/7 emergency service.')

@section('content')
<!-- Hero Section -->
<section class="hero-section" style="min-height: 60vh;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 mx-auto text-center">
                <div class="hero-content text-center d-flex flex-direction: row justify-content-center align-items-center" data-aos="fade-up">
                    <h1 style="color:#000000;">Contact  <span style="color: var(--accent-color);">Velect</span></h1>
                    <p class="subtitle">Wired For Excellence. Powered by Trust</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="section-padding p0">
    <div class="container">
        <div class="row">
            <!-- Contact Form -->
            <div class="col-lg-8 mb-5" data-aos="fade-right">
                <div class="contact-form-card">
                    <h3 class="mb-4">Request a Free Quote</h3>

                    <form id="contactForm" action="{{ route('contact.submit') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="firstName" class="form-label">First Name *</label>
                                <input type="text" class="form-control" id="firstName" name="first_name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="lastName" class="form-label">Last Name *</label>
                                <input type="text" class="form-control" id="lastName" name="last_name" required>
                            </div>
                        </div>
                    
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone *</label>
                                <input type="tel" class="form-control" id="phone" name="phone" required>
                            </div>
                        </div>
                    
                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <input type="text" class="form-control" id="address" name="address" placeholder="City or postal code">
                        </div>
                    
                        <div class="mb-3">
                            <label for="serviceType" class="form-label">Type of Service *</label>
                            <select class="form-select" id="serviceType" name="service_type" required>
                                <option value="">Select a service</option>
                                <option value="residential">Residential Electrical Services</option>
                                <option value="commercial">Commercial Electrical Services</option>
                                <option value="industrial">Industrial Electrical Services</option>
                                <option value="powerpoints">Power Point Installation & Upgrades</option>
                                <option value="lighting">Lighting Installation & Design</option>
                                <option value="switchboard">Switchboard Upgrades & Repairs</option>
                                <option value="faultfinding">Electrical Fault Finding & Repairs</option>
                                <option value="smokealarms">Smoke Alarm Installation & Compliance</option>
                                <option value="cctv">CCTV & Security Camera Installation</option>
                                <option value="datacabling">Data, TV & Phone Cabling</option>
                                <option value="evcharger">EV Charger Installation</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    
                        <div class="mb-3">
                            <label for="urgency" class="form-label">Urgency</label>
                            <select class="form-select" id="urgency" name="urgency">
                                <option value="normal">Normal (within 48h)</option>
                                <option value="urgent">Urgent (within 24h)</option>
                                <option value="emergency">Emergency (immediate)</option>
                            </select>
                        </div>
                    
                        <div class="mb-3">
                            <label for="message" class="form-label">Project Description *</label>
                            <textarea class="form-control" id="message" name="message" rows="5" placeholder="Describe your project in detail..." required></textarea>
                        </div>
                    
                        <div class="mb-3">
                            <label for="attachment" class="form-label">Attach File (optional)</label>
                            <input type="file" class="form-control" id="attachment" name="attachment">
                            <small class="form-text text-muted">Max file size: 5MB. Allowed types: jpg, jpeg, png, pdf, doc, docx.</small>
                        </div>
                    
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="consent" name="consent" required>
                            <label class="form-check-label" for="consent">
                                I agree to be contacted by Velect regarding my request *
                            </label>
                        </div>
                    
                        <button type="submit" class="btn-cta btn-lg">
                            <i class="fas fa-paper-plane"></i> Send my request
                        </button>
                    </form>
                </div>
            </div>

            <!-- Contact Info -->
            <div class="col-lg-4" data-aos="fade-left">
                <div class="contact-info-card mb-4">
                    <h4><i class="fas fa-phone"></i> Phone</h4>
                    <p class="contact-detail">
                        <a href="tel:+61406599271">0406 599 271</a>
                    </p>
                    <p class="contact-note">Available 24/7 for emergencies</p>
                </div>

                <div class="contact-info-card mb-4">
                    <h4><i class="fas fa-envelope"></i> Email</h4>
                    <p class="contact-detail">
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </p>
                    <p class="contact-note">Average response time: 2h</p>
                </div>

                <div class="contact-info-card mb-4">
                    <h4><i class="fas fa-map-marker-alt"></i> Address</h4>
                    <p class="contact-detail">13 Berry St, Clyde NSW 2142</p>
                    <p class="contact-note"> Within 100Km of Sydney Metropolitan Area</p>
                </div>

                <div class="contact-info-card mb-4">
                    <h4><i class="fas fa-clock"></i> Opening Hours</h4>
                    <p class="contact-detail">
                        Mon–Fri: 8:00am - 6:00pm<br>
                        Sat: 9:00am - 5:00pm<br>
                        Sun: Emergencies only
                    </p>
                </div>

                <div class="emergency-card">
                    <h4><i class="fas fa-exclamation-triangle"></i> Emergency?</h4>
                    <p>Power outage, short circuit, safety issue?</p>
                    <a href="tel:+61406599271" class="btn-cta w-100">
                        <i class="fas fa-phone"></i> Emergency Call
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
{{-- <section class="about-section section-padding">
    <div class="container">
        <h2 class="section-title" data-aos="fade-up">Frequently Asked Questions</h2>

        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item" data-aos="fade-up" data-aos-delay="100">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                Is the quote really free?
                            </button>
                        </h2>
                        <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Yes, absolutely! We visit you for free to provide a detailed quote with no obligation on your part.
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item" data-aos="fade-up" data-aos-delay="200">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                Do you really operate 24/7?
                            </button>
                        </h2>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Yes, for electrical emergencies (total outage, short circuit, safety concerns). An extra fee applies for night, weekend, and holiday interventions.
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item" data-aos="fade-up" data-aos-delay="300">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                Are you insured and certified?
                            </button>
                        </h2>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                We are certified RGE Qualibat, Qualifelec, and IRVE. We also hold ten-year and professional liability insurance.
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item" data-aos="fade-up" data-aos-delay="400">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                What are your response times?
                            </button>
                        </h2>
                        <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                For emergencies: within 2h. For scheduled work: usually within 48h to 1 week depending on the project’s complexity.
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item" data-aos="fade-up" data-aos-delay="500">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq5">
                                Do you offer payment plans?
                            </button>
                        </h2>
                        <div id="faq5" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Yes, we offer installment payments for major work. We also help with grant and subsidy applications.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section> --}}
@endsection

@push('styles')
<style>
    .contact-form-card {
        background: var(--secondary-color);
        border-radius: 15px;
        padding: 2.5rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid #e0e0e0;
    }

    .contact-form-card h3 {
        color: var(--primary-color);
        font-weight: 700;
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--accent-color);
        box-shadow: 0 0 0 0.2rem rgba(0, 255, 0, 0.25);
    }

    .contact-info-card {
        background: var(--secondary-color);
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        border: 1px solid #e0e0e0;
    }

    .contact-info-card h4 {
        color: var(--primary-color);
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .contact-info-card h4 i {
        color: var(--accent-color);
        margin-right: 0.5rem;
    }

    .contact-detail {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .contact-detail a {
        color: var(--primary-color);
        text-decoration: none;
    }

    .contact-detail a:hover {
        color: var(--accent-color);
    }

    .contact-note {
        font-size: 0.9rem;
        color: var(--text-light);
        margin: 0;
    }

    .emergency-card {
        background: linear-gradient(135deg, #ff4444, #cc0000);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
    }

    .emergency-card h4 {
        color: white;
        margin-bottom: 1rem;
    }

    .emergency-card p {
        margin-bottom: 1rem;
    }

    .emergency-card .btn-cta {
        background-color: white;
        color: #cc0000;
        border: none;
    }

    .emergency-card .btn-cta:hover {
        background-color: #f0f0f0;
        color: #cc0000;
    }

    .accordion-item {
        border: 1px solid #e0e0e0;
        border-radius: 10px !important;
        margin-bottom: 1rem;
        overflow: hidden;
    }

    .accordion-button {
        background-color: var(--secondary-color);
        color: var(--primary-color);
        font-weight: 600;
        border: none;
        padding: 1.25rem 1.5rem;
    }

    .accordion-button:not(.collapsed) {
        background-color: var(--accent-color);
        color: var(--primary-color);
        box-shadow: none;
    }

    .accordion-button:focus {
        box-shadow: none;
        border-color: transparent;
    }

    .accordion-body {
        background-color: var(--light-gray);
        color: var(--text-light);
        padding: 1.5rem;
    }

    /* Mobile Responsive Fixes */
    @media (max-width: 768px) {
        .contact-form-card {
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .contact-info-card {
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .emergency-card {
            padding: 1rem;
        }

        .hero-section {
            min-height: 40vh !important;
            padding: 6rem 0 2rem 0; /* Top padding to account for fixed navbar */
        }

        .hero-content h1 {
            font-size: 2rem !important;
            line-height: 1.2;
        }

        .hero-content .subtitle {
            font-size: 1rem !important;
        }

        .container {
            padding-left: 15px;
            padding-right: 15px;
        }

        .row {
            margin-left: 0;
            margin-right: 0;
        }

        .col-lg-8, .col-lg-4 {
            padding-left: 0;
            padding-right: 0;
        }

        .form-control, .form-select {
            font-size: 16px; /* Prevents zoom on iOS */
        }

        .btn-cta {
            width: 100%;
            padding: 1rem;
            font-size: 1rem;
        }
    }

    /* Extra small devices */
    @media (max-width: 576px) {
        .contact-form-card {
            padding: 1rem;
        }

        .contact-info-card {
            padding: 0.75rem;
        }

        .emergency-card {
            padding: 0.75rem;
        }

        .hero-content h1 {
            font-size: 1.75rem !important;
        }

        .section-padding {
            padding: 3rem 0;
        }
    }

       @media (max-width: 768px) {
        .hero-section {
            min-height: 50vh !important;
            padding: 2rem 0;
        }
    }
</style>


@endpush

@push('scripts')
<script>
    document.getElementById('contactForm').addEventListener('submit', function(e) {
        e.preventDefault();

        // Simple form validation
        const requiredFields = this.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        if (isValid) {
            // Simulate form submission
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Envoi en cours...';
            submitBtn.disabled = true;

            setTimeout(() => {
                alert('Votre demande a été envoyée avec succès ! Nous vous contacterons sous 24h.');
                this.reset();
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 2000);
        }
    });
</script>
@endpush

