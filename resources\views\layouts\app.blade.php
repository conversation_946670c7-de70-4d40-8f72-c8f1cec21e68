<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="@yield('meta_description', 'Velect - Your trusted electrician. Professional electrical solutions, quality, safety and reliability.')">
    <title>@yield('title', 'Electrical Professionals')</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color:  #000000;
            --secondary-color:  #ffffff;
            --accent-color:  #28AAE1 ;
            --dark-bg: #2c2c2c;
            --light-gray: #E9E8E8;
            --text-light: #6c757d;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Montserrat', sans-serif;
            line-height: 1.6;
            color: var(--primary-color);
        }

        /* Header Styles */
        .navbar {
            padding: 0px;
            /* background-color: white !important; */
             background-color: var(--primary-color) !important; 
            /* padding: 1rem 0; */
            transition: all 0.3s ease;
        }

        .nav-link.active {
                          color: var(--accent-color) !important;

            }
        .logo {
           width: 200px;
           /* height: 60px; */
           margin-right: -85px        }
           /* .navbar-toggler{
            background-color: #000000 !important
           } */

        .navbar-brand {
            font-size: 2rem;
            font-weight: 800;
            color: var(--secondary-color) !important;
        }

        .navbar-nav .nav-link {
            /* color:#000000 !important; */
            color: var(--secondary-color) !important;
            font-weight: 500;
            margin: 0 1rem;
            transition: color 0.3s ease;
        }

        .navbar-nav .nav-link:hover , .navbar-nav .nav-link:active{
            color: var(--accent-color) !important;
        }

        /* Navigation Button Styling */
        .navbar-nav .nav-item .btn-cta {
            margin: 0 0.25rem;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            white-space: nowrap;
        }

        .navbar-nav .nav-item:last-child .btn-cta {
            margin-right: 0;
        }

        .btn-cta {
            background-color: var(--accent-color);
            color: var(--primary-color);
            border: none;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            border-radius: 5px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-cta:hover {
            background-color: #f8f9fa;
            color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(243, 242, 242, 0.3);
        }

        /* Hero Section */
        .hero-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            color: var(--secondary-color);
            position: relative;
            overflow: hidden;
        }

        /* Hero Slideshow */
        .hero-slideshow {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .hero-slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            opacity: 0;
            transition: opacity 2s ease-in-out;
        }

        .hero-slide.active {
            opacity: 1;
        }

        .hero-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to right, rgba(0, 0, 0, 0.7), rgba(255, 255, 255, 0)); /* Changed to rgba(255, 255, 255, 0) */
            ;
            z-index: 2;
        }

        .hero-content {
            position: relative;
            z-index: 3;
        }

        .hero-content h1 {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .hero-content .subtitle {
            font-size: 1.5rem;
            font-weight: 300;
            margin-bottom: 2rem;
            color:var(--text-light);
        }
        .hero-content .subtitle1 {
            font-size: 1.5rem;
            font-weight: 300;
            margin-bottom: 2rem;
            color:white;
        }

        .hero-content p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            max-width: 600px;
        }

        /* Section Styles */
        .section-padding {
            padding: 5rem 0;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 3rem;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background-color: var(--accent-color);
        }

        /* Service Cards */
        .service-card {
            background: var(--secondary-color);
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
            border: 1px solid #e0e0e0;
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .service-icon {
            font-size: 3rem;
            color: var(--accent-color);
            margin-bottom: 1rem;
        }

        .service-card h4 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .service-card p {
            color: var(--text-light);
            line-height: 1.6;
        }

        .service-card-compact {
            padding: 1.5rem;
            min-height: 280px;
        }

        .service-card-compact h5 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .service-card-compact p {
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 0;
        }

        .service-card-compact .service-icon {
            margin-bottom: 1rem;
        }

        .service-card-compact .service-icon i {
            font-size: 2.5rem;
        }

        /* About Section */
        .about-section {
            background-color: var(--light-gray);
        }

        /* Testimonials */
        .testimonial-card {
            background: var(--secondary-color);
            border-radius: 10px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin: 1rem 0;
        }

        .testimonial-text {
            font-style: italic;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .testimonial-author {
            font-weight: 600;
            color: var(--primary-color);
        }

        .stars {
            color: #ffc107;
            margin-bottom: 1rem;
        }

        /* Contact Section */
        .contact-section {
            background-color: var(--dark-bg);
            color: var(--secondary-color);
        }

        .contact-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .contact-info h4 {
            color: var(--accent-color);
            margin-bottom: 1rem;
        }

        .contact-info p {
            margin-bottom: 0.5rem;
        }

        .contact-info i {
            color: var(--accent-color);
            margin-right: 0.5rem;
        }

        /* Footer */
        .footer {
            background-color: var(--secondary-color);
            color: var(   --primary-color);
            padding: 3rem 0 1rem;
        }

        .footer h5 {
            color: var(--accent-color);
            margin-bottom: 1rem;
        }

        .footer a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer a:hover {
            color: var(--accent-color);
        }

        /* Global Mobile Fixes */
        * {
            box-sizing: border-box;
        }

        html, body {
            overflow-x: hidden;
            width: 100%;
        }

        .container-fluid {
            padding-left: 15px;
            padding-right: 15px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            /* Fix for navbar covering content */
            .hero-section {
                padding-top: 80px; /* Account for fixed navbar height */
            }

            .hero-content h1 {
                font-size: 2.5rem;
            }

            .hero-content .subtitle {
                font-size: 1.2rem;
            }

            .section-title {
                font-size: 2rem;
            }

            .navbar-nav {
                text-align: center;
                margin-top: 1rem;
            }

            .navbar-nav .nav-item .btn-cta {
                margin: 0.5rem 0.25rem;
                display: inline-block;
                width: auto;
            }

            .navbar-collapse {
                text-align: center;
            }

            .container {
                padding-left: 15px;
                padding-right: 15px;
                max-width: 100%;
            }

            .row {
                margin-left: 0;
                margin-right: 0;
            }

            [class*="col-"] {
                padding-left: 15px;
                padding-right: 15px;
            }

            /* Additional mobile fixes for all pages */
            body {
                padding-top: 0;
            }

            /* Contact page specific mobile fix */
            .contact-form-card,
            .contact-info-card {
                margin-top: 1rem;
            }
        }

        /* Process Steps */
        .process-step {
            position: relative;
            padding: 2rem 1rem;
        }

        .step-number {
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 50px;
            background-color: var(--accent-color);
            color: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.2rem;
            z-index: 2;
        }

        .step-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .process-step h5 {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .process-step p {
            color: var(--text-light);
            line-height: 1.6;
        }

        /* Gallery Styles */
        .gallery-item {
            transition: all 0.3s ease;
        }

        .gallery-item:hover {
            transform: translateY(-5px);
        }

        .gallery-image {
            position: relative;
            overflow: hidden;
            border-radius: 10px;
        }

        .gallery-item:hover .overlay {
            opacity: 1 !important;
        }

        .gallery-content h5 {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        /* Testimonials Carousel */
        .carousel-indicators {
            margin-bottom: 2rem;
        }

        .carousel-indicators button {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: var(--accent-color);
            border: none;
            margin: 0 5px;
        }

        .carousel-control-prev,
        .carousel-control-next {
            width: 50px;
            height: 50px;
            background-color: var(--accent-color);
            border-radius: 50%;
            top: 50%;
            transform: translateY(-50%);
            opacity: 0.8;
        }

        .carousel-control-prev {
            left: -25px;
        }

        .carousel-control-next {
            right: -25px;
        }

        .carousel-control-prev:hover,
        .carousel-control-next:hover {
            opacity: 1;
        }

        .carousel-control-prev-icon,
        .carousel-control-next-icon {
            width: 20px;
            height: 20px;
        }

        /* About Section Enhancements */
        .about-content h3 {
            color: var(--primary-color);
            font-weight: 600;
        }

        .highlight-item h6 {
            color: var(--primary-color);
            font-weight: 600;
            margin: 0;
        }

        .why-choose-image {
            position: relative;
            min-height: 300px;
        }

        /* Animations */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }
        .p0{
            margin-top: 0 !important;
            padding-top: 0px !important;
        }

        @media (max-width: 768px) {
        .hero-section {
            min-height: 100vh !important;
            padding: 2rem 0;
        }
       
        .hero-content{
            margin-top: 60px
        }
        }
        @media (max-width: 989px) {
        .btn-cta {
            margin-bottom: 10px !important
        }
       
        }
      
    </style>

    @stack('styles')
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="{{ route('home') }}">
                <img class="logo" src="img/Velect logo_1.png" alt=""/>

            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                  <li class="nav-item ">
                        <a class="nav-link @if(Request::routeIs('home')) active @endif" href="{{ route('home') }}">Home</a>
                    </li>
                    <li class="nav-item ">
                        <a class="nav-link @if(Request::is('gallery')) active @endif" href="/gallery">Gallery</a>
                    </li>
                    <li class="nav-item ">
                        <a class="nav-link @if(Request::routeIs('about')) active @endif" href="{{ route('about') }}">About</a>
                    </li>
                    <li class="nav-item ">
                        <a class="nav-link @if(Request::routeIs('services')) active @endif" href="{{ route('services') }}">Services</a>
                    </li>
                    <li class="nav-item ">
                        <a class="nav-link @if(Request::routeIs('contact')) active @endif" href="{{ route('contact') }}">Contact</a>
                    </li>
                    <li class="nav-item">
                    <a href="tel:+61406599271" class="btn-cta me-3">
                                            <i class="fas fa-phone"></i> Call
                                        </a>                    </li>
                    <li class="nav-item">
                        <a href="{{ route('contact') }}" class="btn-cta">
                        Request a Quote
                    </a> 
                    </li>
                </ul>

                {{-- <div class="d-flex justify-content-center">
                    <a href="tel:+61406599271" class="btn-cta me-3">
                        <i class="fas fa-phone"></i> Call
                    </a>
                  
                     <a href="{{ route('contact') }}" class="btn-cta">
                        Request a Quote
                    </a> 
                </div> --}}
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 col-md-6 ">
                    <h5> <img class="logo" src="/Velect logo_1.png" alt=""/> </h5>
                    <p>Your trusted Professionals for all your electrical projects. Quality, safety and reliability guaranteed</p>
                   
                </div>

                <div class="col-lg-2 col-md-6 mt-4">
                    <h5>Navigation</h5>
                    <ul class="list-unstyled">
                        <li><a href="{{ route('home') }}">Home</a></li>
                        <li><a href="{{ route('about') }}">About</a></li>
                        <li><a href="{{ route('services') }}">Services</a></li>
                        <li><a href="{{ route('contact') }}">Contact</a></li>
                    </ul>
                </div>

              

                <div class="col-lg-4 col-md-6  mt-4">
                    <h5>Contact</h5>
                    {{-- <a href="tel:+61406599271" class="btn-cta me-3"> --}}
                        <div>  <a href="tel:+61406599271"><i class="fas fa-phone"></i> 0406 599 271</a></div>
                        <div><a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a></div>
                        <div>
                    <p><i class="fas fa-map-marker-alt"></i> 13 Berry St, Clyde NSW 2142</p></div>
                  

                    

                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5>Finding us on</h5>
                   <div class="social-links">
                        <a href=" https://www.facebook.com/share/18rwRyY58X/ https://www.instagram.com/velect.ptyltd?igsh=enV2MTd5ZTIybGNw" class="me-3"><i class="fab fa-facebook-f"></i></a>
                        {{-- <a href="#" class="me-3"><i class="fab fa-instagram"></i></a> --}}
                        <a href="https://www.linkedin.com/in/vernon-saad-0bb290369?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app" class="me-3"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                  

                    

                </div>
            </div>

            <hr class="my-4">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <p>&copy; {{ date('Y') }} Velect. All rights reserved.</p>
                </div>
                {{-- <div class="col-md-6 text-md-end">
                    <a href="#" class="me-3">Legal Notice</a>
                    <a href="#">Privacy Policy</a>
                </div> --}}
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- AOS Animation -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <!-- Custom JS -->
    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.backgroundColor = 'rgba(0, 0, 0, 0.95)';
            } else {
                navbar.style.backgroundColor = 'var(--primary-color)';
            }
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Hero Background Slideshow
        document.addEventListener('DOMContentLoaded', function() {
            const slides = document.querySelectorAll('.hero-slide');
            let currentSlide = 0;

            function nextSlide() {
                slides[currentSlide].classList.remove('active');
                currentSlide = (currentSlide + 1) % slides.length;
                slides[currentSlide].classList.add('active');
            }

            // Change slide every 5 seconds
            if (slides.length > 1) {
                setInterval(nextSlide, 5000);
            }

            // Auto-play testimonials carousel
            const carousel = document.querySelector('#testimonialsCarousel');
            if (carousel) {
                const bsCarousel = new bootstrap.Carousel(carousel, {
                    interval: 5000,
                    wrap: true
                });
            }

            // Gallery hover effects and modal functionality
            const galleryItems = document.querySelectorAll('.gallery-item');
            const galleryImages = [];
            let currentImageIndex = 0;

            galleryItems.forEach((item, index) => {
                const overlay = item.querySelector('.overlay');
                if (overlay) {
                    item.addEventListener('mouseenter', function() {
                        overlay.style.opacity = '1';
                    });
                    item.addEventListener('mouseleave', function() {
                        overlay.style.opacity = '0';
                    });
                }

                // Collect gallery data
                const imageData = {
                    src: item.dataset.image,
                    title: item.dataset.title,
                    description: item.dataset.description
                };
                galleryImages.push(imageData);

                // Add click event for modal
                item.addEventListener('click', function() {
                    currentImageIndex = index;
                    updateModalContent();
                });
            });

            // Modal navigation functions
            function updateModalContent() {
                const modal = document.getElementById('galleryModal');
                const modalImage = document.getElementById('modalImage');
                const modalTitle = document.getElementById('galleryModalLabel');

                if (galleryImages[currentImageIndex]) {
                    const imageData = galleryImages[currentImageIndex];
                    modalImage.src = `{{ asset('') }}${imageData.src}`;
                    modalImage.alt = imageData.title;
                    modalTitle.textContent = imageData.title;
                }
            }

            // Previous/Next navigation
            document.getElementById('prevImage')?.addEventListener('click', function() {
                currentImageIndex = (currentImageIndex - 1 + galleryImages.length) % galleryImages.length;
                updateModalContent();
            });

            document.getElementById('nextImage')?.addEventListener('click', function() {
                currentImageIndex = (currentImageIndex + 1) % galleryImages.length;
                updateModalContent();
            });

            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                const modal = document.getElementById('galleryModal');
                if (modal && modal.classList.contains('show')) {
                    if (e.key === 'ArrowLeft') {
                        document.getElementById('prevImage')?.click();
                    } else if (e.key === 'ArrowRight') {
                        document.getElementById('nextImage')?.click();
                    }
                }
            });
        });
    </script>

    @stack('scripts')
</body>
</html>

